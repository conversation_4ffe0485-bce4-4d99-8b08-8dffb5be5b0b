"use client";

import React, {useState} from "react";
import {RenderCellProps} from "react-data-grid";
import {Button} from "@ui/components/ui/button";
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from "@ui/components/ui/dropdown-menu";
import {ArrowUpRightAndArrowDownLeftFromCenterIcon, BellIcon, BoltIcon, ChevronDownIcon, CircleCheckIcon, CircleExclamationIcon, CircleInfoIcon, CodeMergeIcon, EnvelopeIcon, EyeIcon, LinkIcon, ListIcon, PenToSquareIcon, TrashIcon, TriangleExclamationIcon} from "@ui/components/icons/FontAwesomeRegular";
import {DataViewRow, RGDMeta} from "@ui/components/workspace/main/views/table";
import {useViews} from "@ui/providers/views";
import {useAlert} from "@ui/providers/alert";
import {useWorkspace} from "@ui/providers/workspace";
import useForceRender from "@ui/components/custom-ui/forceRender";
import {useAuth} from "@ui/providers/user";
import {useRouter} from "@ui/context/routerContext";
import {useBroadcast} from "@ui/providers/broadcast";
import {GridRender} from "@ui/components/workspace/main/views/table/renderer/common/gridRender";
import {createActionServices, evaluateButtonState, executeDeclarativeAction, useInputDialog} from "@ui/utils/buttonActionHelpers";
import {ActionContext, ButtonState, DatabaseData, RecordData, TokenData, User, WorkspaceData} from "@ui/utils/buttonAction";
import {ActionButton, ButtonGroupColumn, DbCondition, DbRecordFilter} from "@repo/app-db-utils/src/typings/db";
import {Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle} from "@ui/components/ui/dialog";
import {Input} from "@ui/components/ui/input";
import {useStackedPeek} from "@ui/providers/stackedpeek";
import {useMaybeRecord} from "@ui/providers/record";

export * from '@ui/components/workspace/main/views/table/renderer/fields/buttonGroup/Editor';


export const getActionIcon = (actionType: string) => {
    switch (actionType) {
        case 'sendEmail':
            return <EnvelopeIcon className="size-3"/>;
        case 'openUrl':
            return <LinkIcon className="size-3"/>;
        case 'updateRecord':
            return <PenToSquareIcon className="size-3"/>;
        case 'deleteRecord':
            return <TrashIcon className="size-3"/>;
        case 'showConfirmation':
            return <CircleInfoIcon className="size-3"/>;
        case 'showToast':
            return <CircleCheckIcon className="size-3"/>;
        case 'sendNotification':
            return <BellIcon className="size-3"/>;
        case 'callWorkflow':
            return <CodeMergeIcon className="size-3"/>;
        case 'expandRecord':
            return <ArrowUpRightAndArrowDownLeftFromCenterIcon className="size-3"/>;
        case 'peekRecord':
            return <EyeIcon className="size-3"/>;
        case 'executeIntegrationAction':
            return <BoltIcon className="size-3"/>;
        default:
            return null;
    }
};

// Helper function to get the appropriate icon for a button based on its actions
export const getButtonIcon = (button: ActionButton) => {
    if (!button) return <CircleExclamationIcon className="size-3"/>;

    if (button.actions?.length > 1) {
        return <ListIcon className="size-3"/>;
    } else if (button.actions?.length === 1) {
        return getActionIcon(button.actions[0].actionType);
    } else {
        return <CircleExclamationIcon className="size-3"/>;
    }
};

export const ButtonGroupRenderer = <R, SR>(props: RenderCellProps<R, SR>): React.ReactNode => {
    const {updateRecordValues, deleteRecords, directDeleteRecords} = useViews();
    const {confirm, toast} = useAlert();
    const {databaseStore, workspace} = useWorkspace();
    const {forceRender} = useForceRender();
    const {token, user} = useAuth();
    const router = useRouter();
    const {sendMessage} = useBroadcast();
    const maybeRecord = useMaybeRecord();

    const inputDialog = useInputDialog();

    const [, setNotificationSent] = useState(false);

    const {column} = props;
    const rowData = props.row as DataViewRow;
    const row = rowData.record;

    // The __meta__ property is added by the table component at runtime
    const meta = (column as { __meta__?: RGDMeta })['__meta__'] as RGDMeta;
    const dbColumn = meta.column as ButtonGroupColumn;
    const buttons = dbColumn.buttons || [];
    const {openRecord} = useStackedPeek();

    const database = databaseStore?.[meta.databaseId];

    const context: ActionContext = {
        record: row as unknown as RecordData,
        database: database as unknown as DatabaseData,
        workspace: workspace as unknown as { workspace?: WorkspaceData; id?: string; domain?: string },
        token: token as TokenData,
        user: user as User,
        meta: meta as unknown as { [key: string]: unknown; databaseId?: string },
        databaseId: meta.databaseId,

        parentRecord: maybeRecord ? {
            id: maybeRecord.recordInfo.record.id,
            databaseId: maybeRecord.database.id,
        } : undefined
    };

    const handleButtonClick = async (button: ActionButton) => {
        if (!button?.actions?.length) {
            toast.info("This button has no actions configured");
            return;
        }

        const services = createActionServices({
            updateRecordValues: updateRecordValues as (databaseId: string, recordIds: string[], values: Record<string, unknown>) => Promise<boolean>,
            deleteRecords,
            directDeleteRecords,
            setPeekRecord: (recordId: string, databaseId: string) => openRecord(recordId, databaseId),
            confirm,
            toast,
            router,
            forceRender,
            sendMessage: (message: string) => sendMessage('info', 'action', {message})
        }, inputDialog);

        const urlsToOpen: string[] = [];
        let actionSucceeded = true;

        for (const action of button.actions) {

            // @TODO: Handle actionType validation and error handling
            //@ts-ignore
            const {success, result} = await executeDeclarativeAction(action as { actionType: string; props?: Record<string, unknown> }, context, services, databaseStore);

            if (!success) {
                actionSucceeded = false;
                break;
            }

            if (action.actionType === 'openUrl' && result?.url) {
                urlsToOpen.push(result.url);
            }
        }

        if (urlsToOpen.length > 0) {
            services.toast.success(`Opening ${urlsToOpen.length} URL(s)...`);
            urlsToOpen.forEach(url => {
                window.open(url, '_blank', 'noopener,noreferrer');
            });
        }

        if (actionSucceeded) {
        } else {
        }

        forceRender();
    };

    const buttonStates = buttons.map(button => ({
        button,
        // @TODO: Handle actionType validation and error handling
        //@ts-ignore
        state: evaluateButtonState(button as { actions?: Array<{ actionType: string; props?: Record<string, unknown> }>; visibleIf?: DbCondition[]; enabledIf?: DbCondition[]; visibleIfFilter?: DbRecordFilter; enabledIfFilter?: DbRecordFilter }, row.recordValues || {})
    }));

    const visibleButtons = buttons.filter(button => {
        const buttonState = buttonStates.find(bs => bs.button.id === button.id)?.state;
        return buttonState?.visible !== false;
    });

    if (!visibleButtons.length) {
        return (
            <GridRender rowId={rowData.id}>
                <div className="r-text r-button-group text-xs h-full flex items-center group overflow-hidden button-group-container">
                </div>
            </GridRender>
        );
    }

    return (
        <>
            <GridRender rowId={rowData.id}>
                <div className="r-text r-button-group text-xs h-full flex items-center group overflow-hidden button-group-container">
                    {visibleButtons.length === 1 ? (
                        (() => {
                            const button = visibleButtons[0];
                            const buttonState = buttonStates.find(bs => bs.button.id === button.id)?.state;
                            const hasError = buttonState?.state === ButtonState.ERROR;
                            const isDisabled = buttonState?.state === ButtonState.DISABLED;

                            return (
                                <Button
                                    className={`text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center ${
                                        hasError ? 'bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed' :
                                        isDisabled ? 'bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed' :
                                        ''
                                    }`}
                                    onClick={() => !hasError && !isDisabled && handleButtonClick(button)}
                                    disabled={hasError || isDisabled}
                                    variant="outline"
                                >
                                    {hasError ? (
                                        <TriangleExclamationIcon className="size-3 text-red-500"/>
                                    ) : isDisabled ? (
                                        <TriangleExclamationIcon className="size-3 text-red-500"/>
                                    ) : (
                                            getButtonIcon(button)
                                        )}
                                    <span className="truncate">{button.label || 'Action'}</span>
                                </Button>
                            );
                        })()
                    ) : (
                         <div className="flex items-center gap-1 flex-wrap sm:flex-nowrap w-full">
                             {(() => {
                                 const button = visibleButtons[0];
                                 const buttonState = buttonStates.find(bs => bs.button.id === button.id)?.state;
                                 const hasError = buttonState?.state === ButtonState.ERROR;
                                 const isDisabled = buttonState?.state === ButtonState.DISABLED;

                                 return (
                                     <Button
                                         className={`text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center ${
                                             hasError ? 'bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed' :
                                             isDisabled ? 'bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed' :
                                             ''
                                         }`}
                                         onClick={() => !hasError && !isDisabled && handleButtonClick(button)}
                                         disabled={hasError || isDisabled}
                                         variant="outline"
                                     >
                                         {hasError ? (
                                             <TriangleExclamationIcon className="size-3 text-red-500"/>
                                         ) : isDisabled ? (
                                             <TriangleExclamationIcon className="size-3 text-red-500"/>
                                         ) : (
                                                 getButtonIcon(button)
                                             )}
                                         <span className="truncate">{button.label || 'Action'}</span>
                                     </Button>
                                 );
                             })()}
                             <DropdownMenu>
                                 <DropdownMenuTrigger asChild>
                                     <Button
                                         className="text-xs rounded-full p-1.5 h-auto font-semibold flex items-center"
                                         variant="outline"
                                     >
                                         <ChevronDownIcon className="size-3"/>
                                     </Button>
                                 </DropdownMenuTrigger>
                                 <DropdownMenuContent>
                                     {visibleButtons.slice(1).map((button, index) => {
                                         const buttonState = buttonStates.find(bs => bs.button.id === button.id)?.state;
                                         const hasError = buttonState?.state === ButtonState.ERROR;
                                         const isDisabled = buttonState?.state === ButtonState.DISABLED;

                                         return (
                                             <DropdownMenuItem
                                                 key={index}
                                                 className={`text-xs font-semibold gap-1 flex items-center ${
                                                     hasError ? 'text-gray-600 cursor-not-allowed' :
                                                     isDisabled ? 'text-gray-400 cursor-not-allowed' :
                                                     ''
                                                 }`}
                                                 onClick={(e) => {
                                                     e.stopPropagation();
                                                     if (!hasError && !isDisabled) {
                                                         handleButtonClick(button);
                                                     }
                                                 }}
                                                 disabled={hasError || isDisabled}
                                             >
                                                 {hasError ? (
                                                     <TriangleExclamationIcon className="size-3 text-red-500"/>
                                                 ) : isDisabled ? (
                                                     <TriangleExclamationIcon className="size-3 text-red-500"/>
                                                 ) : (
                                                         getButtonIcon(button)
                                                     )}
                                                 <span className="truncate">{button.label || 'Action'}</span>
                                             </DropdownMenuItem>
                                         );
                                     })}
                                 </DropdownMenuContent>
                             </DropdownMenu>
                         </div>
                     )}
                </div>
            </GridRender>

            <Dialog open={inputDialog.inputDialogOpen} onOpenChange={inputDialog.setInputDialogOpen}>
                <DialogContent className="max-w-[95vw] sm:max-w-[600px] !rounded-none p-4">
                    <DialogHeader>
                        <DialogTitle>{inputDialog.inputDialogTitle}</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        <p className="text-sm text-gray-500 mb-4">{inputDialog.inputDialogMessage}</p>
                        <Input
                            value={inputDialog.inputValue}
                            onChange={(e) => inputDialog.setInputValue(e.target.value)}
                            placeholder="Enter your input here"
                        />
                    </div>
                    <DialogFooter>
                        <Button variant="outline" size="sm" className="text-xs" onClick={() => inputDialog.setInputDialogOpen(false)}>Cancel</Button>
                        <Button variant="outline" size="sm" className="text-xs" onClick={inputDialog.handleInputSubmit}>Submit</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

